// Configuration and Constants
var AppConfig = {
    // WAX Endpoints
    endpoints: [
        'https://wax.api.atomicassets.io',
        'https://api.wax-aa.bountyblok.io',
        'https://wax.greymass.com'
    ],
    
    currentEndpointIndex: 0,
    
    // Collection schemas
    schemas: {
        harrietsteas: {
            collection: 'harrietsteas',
            schema: 'collectibles',
            name: '<PERSON>\'s Teas'
        },
        pseudai_research: {
            collection: 'pseudaimusic',
            schema: 'research',
            name: 'Pseudai Research'
        },
        pseudai_series: {
            collection: 'pseudaimusic',
            schema: 'series',
            name: 'Pseudai Series'
        }
    },
    
    // API settings
    api: {
        maxPages: 5,
        itemsPerPage: 100,
        timeout: 10000
    },
    
    // UI settings
    ui: {
        thumbnailSizes: ['small', 'medium', 'large'],
        currentThumbnailSize: 0,
        animationDuration: 300
    },
    
    // Get current endpoint
    getCurrentEndpoint: function() {
        return this.endpoints[this.currentEndpointIndex];
    },
    
    // Rotate to next endpoint
    rotateEndpoint: function() {
        this.currentEndpointIndex = (this.currentEndpointIndex + 1) % this.endpoints.length;
        return this.getCurrentEndpoint();
    },
    
    // Get next thumbnail size
    getNextThumbnailSize: function() {
        this.ui.currentThumbnailSize = (this.ui.currentThumbnailSize + 1) % this.ui.thumbnailSizes.length;
        return this.ui.thumbnailSizes[this.ui.currentThumbnailSize];
    },
    
    // Build API URL
    buildApiUrl: function(collection, schema, owner, page = 1) {
        var endpoint = this.getCurrentEndpoint();
        return endpoint + '/atomicassets/v1/assets' +
               '?collection_name=' + collection +
               '&schema_name=' + schema +
               '&owner=' + owner +
               '&page=' + page +
               '&limit=' + this.api.itemsPerPage +
               '&order=desc&sort=asset_id';
    }
};

// Global state management
var AppState = {
    // User data
    user: {
        account: null,
        isLoggedIn: false
    },
    
    // Current collection data
    collection: {
        name: null,
        schema: null,
        assets: [],
        currentAssetIndex: 0
    },
    
    // UI state
    ui: {
        isLoading: false,
        currentView: 'login', // 'login', 'schema', 'gallery'
        thumbnailSize: 'medium'
    },
    
    // Set user
    setUser: function(account) {
        this.user.account = account;
        this.user.isLoggedIn = true;
    },
    
    // Clear user
    clearUser: function() {
        this.user.account = null;
        this.user.isLoggedIn = false;
        this.collection.assets = [];
        this.collection.currentAssetIndex = 0;
    },
    
    // Set collection data
    setCollection: function(name, schema, assets) {
        this.collection.name = name;
        this.collection.schema = schema;
        this.collection.assets = assets || [];
        this.collection.currentAssetIndex = 0;
    },
    
    // Get current asset
    getCurrentAsset: function() {
        if (this.collection.assets.length === 0) return null;
        return this.collection.assets[this.collection.currentAssetIndex];
    },
    
    // Navigate to next asset
    nextAsset: function() {
        if (this.collection.assets.length === 0) return;
        this.collection.currentAssetIndex = (this.collection.currentAssetIndex + 1) % this.collection.assets.length;
    },
    
    // Navigate to previous asset
    prevAsset: function() {
        if (this.collection.assets.length === 0) return;
        this.collection.currentAssetIndex = this.collection.currentAssetIndex === 0 
            ? this.collection.assets.length - 1 
            : this.collection.currentAssetIndex - 1;
    },
    
    // Set asset by index
    setAssetIndex: function(index) {
        if (index >= 0 && index < this.collection.assets.length) {
            this.collection.currentAssetIndex = index;
        }
    }
};

// Utility functions
var Utils = {
    // Show loading state
    showLoading: function(message) {
        var loadingText = document.querySelector('.loading-text');
        if (loadingText) {
            loadingText.textContent = message || 'Loading...';
        }
        
        var loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.classList.remove('hidden');
        }
        
        AppState.ui.isLoading = true;
    },
    
    // Hide loading state
    hideLoading: function() {
        var loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.classList.add('hidden');
        }
        
        AppState.ui.isLoading = false;
    },
    
    // Show element with animation
    showElement: function(element, animationClass) {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        
        if (element) {
            element.classList.remove('hidden');
            if (animationClass) {
                element.classList.add(animationClass);
            }
        }
    },
    
    // Hide element
    hideElement: function(element) {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        
        if (element) {
            element.classList.add('hidden');
        }
    },
    
    // Format asset name
    formatAssetName: function(asset) {
        var name = asset.name || 'Unnamed Asset';
        var mint = asset.template_mint ? '#' + asset.template_mint : '';
        return name + ' ' + mint;
    },
    
    // Get IPFS image URL
    getImageUrl: function(ipfsHash) {
        return 'https://ipfs.io/ipfs/' + ipfsHash;
    },
    
    // Debounce function
    debounce: function(func, wait) {
        var timeout;
        return function executedFunction() {
            var context = this;
            var args = arguments;
            var later = function() {
                timeout = null;
                func.apply(context, args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Show error message
    showError: function(message) {
        console.error('Error:', message);
        // You could implement a toast notification system here
        alert('Error: ' + message);
    }
};
