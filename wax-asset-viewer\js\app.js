// Main application controller
var App = {
    initialized: false,
    
    // Initialize the application
    initialize: function() {
        if (this.initialized) return;
        
        console.log('Initializing WAX Asset Viewer...');
        
        // Show loading screen
        Utils.showLoading('Initializing application...');
        
        // Initialize components in sequence
        this.initializeComponents()
            .then(function() {
                console.log('Application initialized successfully');
                App.initialized = true;
                App.onInitializationComplete();
            })
            .catch(function(error) {
                console.error('Application initialization failed:', error);
                App.onInitializationError(error);
            });
    },
    
    // Initialize all components
    initializeComponents: function() {
        return new Promise(function(resolve, reject) {
            try {
                // Initialize WAX authentication
                if (!WaxAuth.initialize()) {
                    throw new Error('Failed to initialize WAX authentication');
                }
                
                // Initialize API
                EnhancedAPI.initialize()
                    .then(function() {
                        // Initialize UI components
                        App.initializeUI();
                        
                        // Initialize event handlers
                        App.initializeEventHandlers();
                        
                        resolve();
                    })
                    .catch(function(error) {
                        reject(error);
                    });
                
            } catch (error) {
                reject(error);
            }
        });
    },
    
    // Initialize UI components
    initializeUI: function() {
        // Initialize authentication UI
        AuthUI.initialize();
        
        // Initialize endpoint selector
        this.initializeEndpointSelector();
        
        // Set initial UI state
        this.updateUIState();
        
        console.log('UI components initialized');
    },
    
    // Initialize endpoint selector
    initializeEndpointSelector: function() {
        var endpointSelect = document.getElementById('endpoint-select');
        var rotateBtn = document.getElementById('rotate-endpoint');
        
        if (endpointSelect) {
            // Set current endpoint
            endpointSelect.value = AppConfig.getCurrentEndpoint();
            
            // Handle endpoint change
            endpointSelect.addEventListener('change', function() {
                var selectedEndpoint = endpointSelect.value;
                var index = AppConfig.endpoints.indexOf(selectedEndpoint);
                if (index !== -1) {
                    AppConfig.currentEndpointIndex = index;
                    console.log('Endpoint changed to:', selectedEndpoint);
                }
            });
        }
        
        if (rotateBtn) {
            rotateBtn.addEventListener('click', function() {
                var newEndpoint = AppConfig.rotateEndpoint();
                if (endpointSelect) {
                    endpointSelect.value = newEndpoint;
                }
                console.log('Endpoint rotated to:', newEndpoint);
                
                // Add visual feedback
                rotateBtn.style.transform = 'rotate(180deg)';
                setTimeout(function() {
                    rotateBtn.style.transform = '';
                }, 300);
            });
        }
    },
    
    // Initialize global event handlers
    initializeEventHandlers: function() {
        // Handle window resize
        window.addEventListener('resize', Utils.debounce(function() {
            App.onWindowResize();
        }, 250));
        
        // Handle visibility change
        document.addEventListener('visibilitychange', function() {
            App.onVisibilityChange();
        });
        
        // Handle online/offline status
        window.addEventListener('online', function() {
            App.onConnectionChange(true);
        });
        
        window.addEventListener('offline', function() {
            App.onConnectionChange(false);
        });
        
        console.log('Event handlers initialized');
    },
    
    // Handle initialization completion
    onInitializationComplete: function() {
        // Hide loading screen
        Utils.hideLoading();
        
        // Show app container
        Utils.showElement('app-container', 'fade-in');
        
        // Check if user was previously logged in (could implement session persistence)
        this.checkPreviousSession();
        
        console.log('WAX Asset Viewer ready');
    },
    
    // Handle initialization error
    onInitializationError: function(error) {
        Utils.hideLoading();
        Utils.showError('Failed to initialize application: ' + error.message);
        
        // Show app container anyway with limited functionality
        Utils.showElement('app-container');
    },
    
    // Check for previous session
    checkPreviousSession: function() {
        // Could implement localStorage session persistence here
        // For now, always start with login screen
        this.updateUIState();
    },
    
    // Update UI state based on app state
    updateUIState: function() {
        if (WaxAuth.isLoggedIn()) {
            Utils.hideElement('login-section');
            Utils.showElement('main-content');
            AppState.ui.currentView = 'schema';
        } else {
            Utils.showElement('login-section');
            Utils.hideElement('main-content');
            AppState.ui.currentView = 'login';
        }
    },
    
    // Handle window resize
    onWindowResize: function() {
        // Could implement responsive adjustments here
        console.log('Window resized');
    },
    
    // Handle visibility change
    onVisibilityChange: function() {
        if (document.hidden) {
            console.log('App hidden');
        } else {
            console.log('App visible');
        }
    },
    
    // Handle connection change
    onConnectionChange: function(online) {
        if (online) {
            console.log('Connection restored');
            // Could retry failed requests here
        } else {
            console.log('Connection lost');
            Utils.showError('Internet connection lost');
        }
    },
    
    // Restart application
    restart: function() {
        console.log('Restarting application...');
        
        // Clear state
        AppState.clearUser();
        
        // Reset UI
        this.updateUIState();
        
        // Could clear caches here
        console.log('Application restarted');
    }
};

// UI utility functions
var UI = {
    // Show login section
    showLoginSection: function() {
        Utils.showElement('login-section', 'fade-in');
        Utils.hideElement('main-content');
        AppState.ui.currentView = 'login';
    },
    
    // Hide main content
    hideMainContent: function() {
        Utils.hideElement('main-content');
        Utils.hideElement('gallery-section');
    },
    
    // Show main content
    showMainContent: function() {
        Utils.showElement('main-content', 'fade-in');
        AppState.ui.currentView = 'schema';
    }
};

// Initialize app when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing app...');
    App.initialize();
});

// Handle page unload
window.addEventListener('beforeunload', function() {
    console.log('Application shutting down...');
    // Could save state here
});

// Export for debugging
window.WaxAssetViewer = {
    App: App,
    AppState: AppState,
    AppConfig: AppConfig,
    WaxAuth: WaxAuth,
    Gallery: Gallery,
    Utils: Utils
};
