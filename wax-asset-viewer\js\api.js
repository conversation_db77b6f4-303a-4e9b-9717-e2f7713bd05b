// API functions for fetching WAX assets
var WaxAPI = {
    // Fetch assets from a collection
    fetchAssets: function(collection, schema, owner) {
        return new Promise(function(resolve, reject) {
            var promises = [];
            var allAssets = [];
            var currentPage = 1;
            var maxPages = AppConfig.api.maxPages;
            
            // Create promises for all pages
            while (currentPage <= maxPages) {
                var url = AppConfig.buildApiUrl(collection, schema, owner, currentPage);
                promises.push(WaxAPI.fetchPage(url));
                currentPage++;
            }
            
            // Execute all requests
            Promise.all(promises)
                .then(function(responses) {
                    responses.forEach(function(response) {
                        if (response && response.data && Array.isArray(response.data)) {
                            allAssets = allAssets.concat(response.data);
                        }
                    });
                    
                    console.log('Fetched ' + allAssets.length + ' assets from ' + collection + '/' + schema);
                    resolve(allAssets);
                })
                .catch(function(error) {
                    console.error('Error fetching assets:', error);
                    reject(error);
                });
        });
    },
    
    // Fetch a single page of results
    fetchPage: function(url) {
        return new Promise(function(resolve, reject) {
            var xhr = new XMLHttpRequest();
            xhr.timeout = AppConfig.api.timeout;
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (e) {
                            console.error('Error parsing JSON:', e);
                            resolve({ data: [] });
                        }
                    } else {
                        console.warn('HTTP error ' + xhr.status + ' for URL: ' + url);
                        resolve({ data: [] });
                    }
                }
            };
            
            xhr.onerror = function() {
                console.error('Network error for URL: ' + url);
                resolve({ data: [] });
            };
            
            xhr.ontimeout = function() {
                console.error('Timeout for URL: ' + url);
                resolve({ data: [] });
            };
            
            xhr.open('GET', url, true);
            xhr.send();
        });
    },
    
    // Test endpoint connectivity
    testEndpoint: function(endpoint) {
        return new Promise(function(resolve, reject) {
            var testUrl = endpoint + '/atomicassets/v1/collections?limit=1';
            var xhr = new XMLHttpRequest();
            xhr.timeout = 5000; // 5 second timeout for testing
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        resolve(true);
                    } else {
                        resolve(false);
                    }
                }
            };
            
            xhr.onerror = function() {
                resolve(false);
            };
            
            xhr.ontimeout = function() {
                resolve(false);
            };
            
            xhr.open('GET', testUrl, true);
            xhr.send();
        });
    },
    
    // Find working endpoint
    findWorkingEndpoint: function() {
        return new Promise(function(resolve, reject) {
            var endpoints = AppConfig.endpoints.slice(); // Copy array
            var testPromises = endpoints.map(function(endpoint) {
                return WaxAPI.testEndpoint(endpoint);
            });
            
            Promise.all(testPromises)
                .then(function(results) {
                    for (var i = 0; i < results.length; i++) {
                        if (results[i]) {
                            AppConfig.currentEndpointIndex = i;
                            console.log('Using endpoint:', endpoints[i]);
                            resolve(endpoints[i]);
                            return;
                        }
                    }
                    
                    // If no endpoint works, use the first one anyway
                    console.warn('No endpoints responding, using default');
                    AppConfig.currentEndpointIndex = 0;
                    resolve(endpoints[0]);
                })
                .catch(function(error) {
                    console.error('Error testing endpoints:', error);
                    resolve(endpoints[0]);
                });
        });
    },
    
    // Retry with different endpoint
    retryWithNextEndpoint: function(collection, schema, owner) {
        return new Promise(function(resolve, reject) {
            var originalEndpoint = AppConfig.currentEndpointIndex;
            var attempts = 0;
            var maxAttempts = AppConfig.endpoints.length;
            
            function tryNextEndpoint() {
                if (attempts >= maxAttempts) {
                    reject(new Error('All endpoints failed'));
                    return;
                }
                
                AppConfig.rotateEndpoint();
                attempts++;
                
                console.log('Trying endpoint ' + attempts + '/' + maxAttempts + ':', AppConfig.getCurrentEndpoint());
                
                WaxAPI.fetchAssets(collection, schema, owner)
                    .then(function(assets) {
                        if (assets && assets.length > 0) {
                            resolve(assets);
                        } else {
                            tryNextEndpoint();
                        }
                    })
                    .catch(function(error) {
                        console.warn('Endpoint failed:', AppConfig.getCurrentEndpoint(), error);
                        tryNextEndpoint();
                    });
            }
            
            tryNextEndpoint();
        });
    }
};

// Enhanced fetch function with automatic endpoint rotation
var EnhancedAPI = {
    // Fetch assets with automatic retry
    fetchAssetsWithRetry: function(collection, schema, owner) {
        return new Promise(function(resolve, reject) {
            Utils.showLoading('Fetching assets from ' + collection + '...');
            
            WaxAPI.fetchAssets(collection, schema, owner)
                .then(function(assets) {
                    if (assets && assets.length > 0) {
                        Utils.hideLoading();
                        resolve(assets);
                    } else {
                        console.log('No assets found, trying other endpoints...');
                        Utils.showLoading('Trying alternative endpoints...');
                        
                        WaxAPI.retryWithNextEndpoint(collection, schema, owner)
                            .then(function(retryAssets) {
                                Utils.hideLoading();
                                resolve(retryAssets);
                            })
                            .catch(function(error) {
                                Utils.hideLoading();
                                Utils.showError('Failed to fetch assets from all endpoints');
                                reject(error);
                            });
                    }
                })
                .catch(function(error) {
                    console.log('Primary endpoint failed, trying alternatives...');
                    Utils.showLoading('Trying alternative endpoints...');
                    
                    WaxAPI.retryWithNextEndpoint(collection, schema, owner)
                        .then(function(retryAssets) {
                            Utils.hideLoading();
                            resolve(retryAssets);
                        })
                        .catch(function(retryError) {
                            Utils.hideLoading();
                            Utils.showError('Failed to fetch assets from all endpoints');
                            reject(retryError);
                        });
                });
        });
    },
    
    // Initialize API with working endpoint
    initialize: function() {
        return new Promise(function(resolve, reject) {
            Utils.showLoading('Finding best endpoint...');
            
            WaxAPI.findWorkingEndpoint()
                .then(function(endpoint) {
                    Utils.hideLoading();
                    console.log('API initialized with endpoint:', endpoint);
                    resolve(endpoint);
                })
                .catch(function(error) {
                    Utils.hideLoading();
                    console.error('Failed to initialize API:', error);
                    reject(error);
                });
        });
    }
};
